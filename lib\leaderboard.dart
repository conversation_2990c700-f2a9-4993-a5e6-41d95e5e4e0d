// ignore_for_file: unnecessary_string_interpolations

import 'package:bibl/models/league_user_model.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controllers/profile_controller.dart';
import 'services/leaderboard_service.dart';
import 'widgets/customappbar.dart';
import 'widgets/leaderboard_widgets.dart';
import 'widgets/league_clock_widget.dart';

class Leaderboard extends StatefulWidget {
  const Leaderboard({super.key});

  @override
  State<Leaderboard> createState() => _LeaderboardState();
}

class _LeaderboardState extends State<Leaderboard> {
  final ProfileController profileController = Get.find();

  Future<void> _assignUserToBronzana(String userId, String username) async {
    try {
      final leaderboardService = LeaderboardService(userId);
      await leaderboardService.addUserToLeague(
        username: username,
        targetLeague: '<PERSON><PERSON><PERSON><PERSON>',
      );
    } catch (e) {
      debugPrint('Error assigning user to <PERSON><PERSON>zana: $e');
      Get.snackbar('Error', 'Failed to assign to Bronzana League: $e');
    }
  }

  /// Fix all users who don't have uniqueName (for testing)
  Future<void> _fixAllUsernames() async {
    try {
      final firestore = FirebaseFirestore.instance;

      // Step 1: Fix users collection
      final usersSnapshot = await firestore.collection('users').get();
      final userUpdates = <String, String>{}; // userId -> uniqueName

      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data();
        final uniqueName = userData['uniqueName'] as String?;
        final name = userData['name'] as String? ?? 'User';

        if (uniqueName == null || uniqueName.isEmpty) {
          await profileController.generateUniqueUsername(userDoc.id, name);

          // Get the generated username
          final updatedUserDoc =
              await firestore.collection('users').doc(userDoc.id).get();
          final newUniqueName = updatedUserDoc.data()?['uniqueName'] as String?;
          if (newUniqueName != null) {
            userUpdates[userDoc.id] = newUniqueName;
          }
          debugPrint(
              'Fixed username for user: ${userDoc.id} -> $newUniqueName');
        }
      }

      // Step 2: Update leaderboard collections
      final leagues = [
        'Bronzana',
        'Srebrna',
        'Zlatna',
        'Platinum',
        'Dijamantska',
        'Rubin',
        'Safirna',
        'Smaragdna',
        'Topaz',
        'Ametist',
        'Opal',
        'Kvarc',
        'Žad',
        'Kristalna',
        'Fenix',
        'Oniks',
        'Galaktička',
        'Koralska',
        'Jupiter',
        'Elitna'
      ];

      for (final league in leagues) {
        final playersSnapshot = await firestore
            .collection('leaderboards')
            .doc(league)
            .collection('players')
            .get();

        for (final playerDoc in playersSnapshot.docs) {
          final playerId = playerDoc.id;
          final playerData = playerDoc.data();
          final currentUsername = playerData['username'] as String?;

          if (userUpdates.containsKey(playerId)) {
            // Update with the new username
            await playerDoc.reference.update({
              'username': userUpdates[playerId]!,
            });
            debugPrint(
                'Updated leaderboard username for $playerId in $league: ${userUpdates[playerId]}');
          } else if (currentUsername == null ||
              currentUsername.isEmpty ||
              currentUsername == 'Unknown') {
            // Try to get username from users collection
            final userDoc =
                await firestore.collection('users').doc(playerId).get();
            if (userDoc.exists) {
              final uniqueName = userDoc.data()?['uniqueName'] as String?;
              if (uniqueName != null && uniqueName.isNotEmpty) {
                await playerDoc.reference.update({
                  'username': uniqueName,
                });
                debugPrint(
                    'Updated leaderboard username for $playerId in $league: $uniqueName');
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error fixing usernames: $e');
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return const Scaffold(
        body: Center(child: Text('Please log in to view the leaderboard.')),
      );
    }

    final userID = user.uid;

    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          isBackButton: false,
          widget: null,
          title: 'umniLab lige',
        ),
        body: StreamBuilder<DocumentSnapshot>(
          stream: FirebaseFirestore.instance
              .collection('users')
              .doc(userID)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                  child: CircularProgressIndicator(color: mainColor));
            }

            if (snapshot.hasError) {
              return const Center(child: Text('Error loading user data.'));
            }

            if (!snapshot.hasData || !snapshot.data!.exists) {
              return const Center(child: Text('User not found.'));
            }

            final userData = snapshot.data!.data() as Map<String, dynamic>;
            String league = userData['league'] ?? 'Bronzana';

            // If league is empty, assign to Bronzana
            if (league.isEmpty) {
              league = 'Bronzana';
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                try {
                  await _assignUserToBronzana(
                      userID, profileController.userr.value.uniqueName ?? '');
                } catch (e) {
                  debugPrint('Error assigning user to Bronzana: $e');
                }
              });
            }

            return Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Column(
                          children: [
                            const SizedBox(height: 16),
                            const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Txt(
                                  txt:
                                      'Click this button to check what happens after the week ends',
                                  maxLines: 2,
                                  fontSize: 16),
                            ),
                            ElevatedButton(
                              onPressed: () async {
                                try {
                                  await FirebaseFunctions.instance
                                      .httpsCallable('manualUpdateLeaderboard')
                                      .call();
                                } catch (e) {
                                  Get.snackbar('Error',
                                      'Failed to update leaderboard: $e');
                                }
                              },
                              child: const Text('Update Leaderboard'),
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: () async {
                                try {
                                  await _fixAllUsernames();
                                  Get.snackbar(
                                      'Success', 'Fixed all usernames!');
                                } catch (e) {
                                  Get.snackbar(
                                      'Error', 'Failed to fix usernames: $e');
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                              ),
                              child: const Text('Fix All Usernames'),
                            ),
                            Expanded(
                              child: StreamBuilder<QuerySnapshot>(
                                stream: FirebaseFirestore.instance
                                    .collection('leaderboards')
                                    .doc(league)
                                    .collection('players')
                                    .orderBy('score', descending: true)
                                    .limit(10)
                                    .snapshots(),
                                builder: (context, snapshot) {
                                  if (snapshot.connectionState ==
                                          ConnectionState.waiting ||
                                      snapshot.hasError) {
                                    return const Center(
                                      child: CircularProgressIndicator(
                                        color: mainColor,
                                      ),
                                    );
                                  }

                                  final players = snapshot.data!.docs
                                      .map((doc) =>
                                          LeaguePlayerModel.fromSnap(doc))
                                      .toList();

                                  final currentUserId =
                                      profileController.userr.value.uid ?? '';
                                  final isUserInLeague = players.any((player) =>
                                      player.playerId == currentUserId);

                                  if (!isUserInLeague &&
                                      currentUserId.isNotEmpty) {
                                    // Use leaderboard service for proper user assignment
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) async {
                                      try {
                                        final leaderboardService =
                                            LeaderboardService(currentUserId);
                                        await leaderboardService
                                            .addUserToLeague(
                                          username: profileController
                                                  .userr.value.uniqueName ??
                                              'Unknown',
                                          targetLeague: league,
                                        );
                                      } catch (e) {
                                        debugPrint(
                                            'Error adding user to league: $e');
                                      }
                                    });
                                  }

                                  if (players.isEmpty) {
                                    return const Center(
                                        child: Text(
                                            'No players found in this group.'));
                                  }

                                  return ListView(
                                    padding: const EdgeInsets.all(16.0),
                                    children: [
                                      leaderboardListItem(players),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(child: leagueClockWidget()),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget leaderboardListItem(List<LeaguePlayerModel> players) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const RankHeaderWidget(
          isOnResult: false,
        ),
        const SizedBox(height: 16),
        const Text(
          'Tabela',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        DataTable(
          columns: const [
            DataColumn(label: Text('Rank')),
            DataColumn(label: Text('Ime')),
            DataColumn(label: Text('Neuroni')),
          ],
          rows: players.map((player) {
            final currentUid = profileController.userr.value.uid ?? '';
            final currentName = profileController.userr.value.uniqueName ?? '';
            final isCurrentUser = player.playerId == currentUid;
            final textColor =
                isCurrentUser ? const Color(0xff7CE099) : Colors.black;

            return DataRow(
              cells: [
                DataCell(Text(
                  '#${players.indexOf(player) + 1}',
                  style: TextStyle(color: textColor),
                )),
                DataCell(Text(
                  isCurrentUser ? currentName : (player.username ?? 'N/A'),
                  style: TextStyle(color: textColor),
                )),
                DataCell(Text(
                  player.score?.toString() ?? '0',
                  style: TextStyle(color: textColor),
                )),
              ],
            );
          }).toList(),
        ),
        Container(height: 150, color: Colors.transparent),
      ],
    );
  }
}
