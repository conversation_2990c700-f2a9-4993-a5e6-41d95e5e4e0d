const { onSchedule } = require('firebase-functions/v2/scheduler');
const { onCall } = require('firebase-functions/v2/https');
const logger = require('firebase-functions').logger;
const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});
const db = admin.firestore();

const HOUR = 60 * 60 * 1000;

// resetUserProgressIfInactive (runs daily)
exports.resetUserProgressIfInactive = onSchedule('every 24 hours', async (event) => {
    try {
        const now = new Date();
        const usersSnapshot = await db.collection('users').get();
        const heartsSnapshot = await db.collection('hearts').limit(1).get();

        let defaultHearts = 3; // fallback if kuch na mila
        
        if (!heartsSnapshot.empty) {
          const firstDoc = heartsSnapshot.docs[0];
          defaultHearts = firstDoc.data().defaultHearts ?? 3;
        }

        for (const userDoc of usersSnapshot.docs) {
            const userId = userDoc.id;
            const user = userDoc.data();
            const lastActive = user.lastActiveTime ? user.lastActiveTime.toDate() : null;

            if (!lastActive) continue;

            const inactiveDays = (now - lastActive) / (1000 * 60 * 60 * 24);

            if (inactiveDays >= 30) {
                await db.collection('users').doc(userId).update({
                    neurons: 0,
                    hearts: defaultHearts,
                    league: 'Bronzana',
                });

                // Also remove from current league leaderboard
                const league = user.league || 'Bronzana';
                await db.collection('leaderboards')
                        .doc(league)
                        .collection('players')
                        .doc(userId)
                        .delete();

                // Add to Bronzana league with 0 score
                await db.collection('leaderboards')
                        .doc('Bronzana')
                        .collection('players')
                        .doc(userId)
                        .set({
                            playerId: userId,
                            username: user.username || '',
                            league: 'Bronzana',
                            score: 0
                        });

                logger.info(`User ${userId} reset due to 30-day inactivity.`);
            }
        }
    } catch (error) {
        logger.error('Error resetting inactive user progress:', error);
    }
});

// sendInactivityNotifications (keep it scheduled)
exports.sendInactivityNotifications = onSchedule('every 1 hours', async (event) => {
    try {
        const now = new Date();
        const usersSnapshot = await db.collection('users').get();

        for (const doc of usersSnapshot.docs) {
            const user = doc.data();
            const lastActiveTime = user.lastActiveTime ? user.lastActiveTime.toDate() : null;
            const lastSent = user.lastInactivityNotification || null;

            if (!lastActiveTime) continue;

            const timeSinceLastActive = now - lastActiveTime;
            let message = null;
            let tag = null;

            if (timeSinceLastActive > 48 * HOUR && timeSinceLastActive < 49 * HOUR && lastSent !== '48h') {
                message = '📱Ćao, zar ćeš da koristiš socijalne mreže ceo dan?';
                tag = '48h';
            } else if (timeSinceLastActive > 44 * HOUR && timeSinceLastActive < 45 * HOUR && lastSent !== '44h') {
                message = '🔥Nedozvoli da propustiš bonus neurone!🔥 Imaš još 4 sata da bi ostvario bonus';
                tag = '44h';
            } else if (timeSinceLastActive > 32 * HOUR && timeSinceLastActive < 33 * HOUR && lastSent !== '32h') {
                message = '😔Zar ćeš da dopustiš da ispadneš iz lige?';
                tag = '32h';
            } else if (timeSinceLastActive > 24 * HOUR && timeSinceLastActive < 25 * HOUR && lastSent !== '24h') {
                message = '🧠Ćao, vreme je za jednu lekciju, izdvoji 5 minuta da ispuniš dan novim znanjem!🧠';
                tag = '24h';
            } else if (timeSinceLastActive > 7 * 24 * HOUR && lastSent !== '7d') {
                message = 'Dobro, nećemo ti slati više notifikacije, srećno! 👋';
                tag = '7d';
            }

            if (message && user.isNotificationOn && user.deviceToken) {
                const payload = {
                    notification: {
                        title: 'Reminder',
                        body: message,
                    },
                    token: user.deviceToken,
                };
                await admin.messaging().send(payload);

                // Update the last sent tag
                const updateData = {
                    lastInactivityNotification: tag
                };

                // Also turn off future notifications if it's the final message
                if (tag === '7d') {
                    updateData.isNotificationOn = false;
                }

                await db.collection('users').doc(doc.id).update(updateData);
            }
        }

        logger.info('Inactivity notifications sent successfully.');
    } catch (error) {
        logger.error('Error sending notifications:', error);
    }
});

// cleanExpiredOpenedQuizesAndArticles (updated to handle Map with timestamps)
exports.cleanExpiredOpenedQuizesAndArticles = onSchedule('every 24 hours', async (event) => {
    try {
        const now = new Date();
        const usersSnapshot = await db.collection('users').get();

        for (const userDoc of usersSnapshot.docs) {
            const userId = userDoc.id;
            const userData = userDoc.data();
            const openedQuizesAndArticles = userData.openedQuizesAndArticlesinMonth || {};

            // Clean expired entries (older than 30 days)
            const cleanedMap = {};
            let hasExpiredEntries = false;

            for (const [itemId, timestamp] of Object.entries(openedQuizesAndArticles)) {
                if (timestamp && timestamp.toDate) {
                    const openedDate = timestamp.toDate();
                    const daysDifference = Math.floor((now - openedDate) / (1000 * 60 * 60 * 24));

                    if (daysDifference <= 30) {
                        cleanedMap[itemId] = timestamp;
                    } else {
                        hasExpiredEntries = true;
                    }
                } else {
                    // Remove invalid timestamp entries
                    hasExpiredEntries = true;
                }
            }

            // Only update if there were expired entries to remove
            if (hasExpiredEntries) {
                await db.collection('users').doc(userId).update({
                    openedQuizesAndArticlesinMonth: cleanedMap,
                });
            }
        }
        logger.info('Expired opened quizzes and articles cleaned successfully.');
    } catch (error) {
        logger.error('Error cleaning expired opened quizzes and articles:', error);
    }
});

const LEAGUES = [
    'Bronzana', 'Srebrna', 'Zlatna', 'Platinum', 'Dijamantska', 'Rubin',
    'Safirna', 'Smaragdna', 'Topaz', 'Ametist', 'Opal', 'Kvarc', 'Žad',
    'Kristalna', 'Fenix', 'Oniks', 'Galaktička', 'Koralska', 'Jupiter', 'Elitna'
];

// Helper function to get current week start (Monday 00:00)
function getCurrentWeekStart() {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Sunday = 0, Monday = 1
    const monday = new Date(now);
    monday.setDate(now.getDate() - daysToMonday);
    monday.setHours(0, 0, 0, 0);
    return monday;
}

// Helper function to get previous week start
function getPreviousWeekStart() {
    const currentWeekStart = getCurrentWeekStart();
    const previousWeekStart = new Date(currentWeekStart);
    previousWeekStart.setDate(currentWeekStart.getDate() - 7);
    return previousWeekStart;
}

// Helper function to get previous week's league standings
async function getPreviousWeekStandings(firestore, previousWeekStart) {
    const standings = {};

    for (const league of LEAGUES) {
        standings[league] = [];

        try {
            const groupsSnapshot = await firestore
                .collection('leaderboards')
                .doc(league)
                .collection('groups')
                .get();

            for (const groupDoc of groupsSnapshot.docs) {
                const playersSnapshot = await groupDoc.ref
                    .collection('players')
                    .orderBy('score', 'desc')
                    .orderBy('lastUpdated', 'asc')
                    .get();

                const groupPlayers = playersSnapshot.docs.map((doc, index) => ({
                    ...doc.data(),
                    id: doc.id,
                    groupId: groupDoc.id,
                    position: index + 1
                }));

                standings[league].push(...groupPlayers);
            }
        } catch (error) {
            logger.error(`Error getting standings for ${league}:`, error);
        }
    }

    return standings;
}

// Helper function to calculate league assignments with proper promotion/demotion
async function calculateLeagueAssignments(firestore, users, leagueStandings) {
    const assignments = {};
    LEAGUES.forEach(league => { assignments[league] = []; });

    for (const user of users) {
        const currentLeague = user.league || 'Bronzana';
        const userId = user.id;

        // Find user's previous week performance
        let userStanding = null;
        if (leagueStandings[currentLeague]) {
            userStanding = leagueStandings[currentLeague].find(player => player.id === userId);
        }

        let newLeague = currentLeague;

        if (userStanding) {
            // Apply promotion/demotion rules
            if (userStanding.position <= 3 && currentLeague !== 'Elitna') {
                // Top 3 get promoted (except if already in highest league)
                const currentIndex = LEAGUES.indexOf(currentLeague);
                if (currentIndex < LEAGUES.length - 1) {
                    newLeague = LEAGUES[currentIndex + 1];
                }
            } else if (userStanding.position >= 8 && currentLeague !== 'Bronzana') {
                // Bottom 3 (positions 8-10) get demoted (except if in lowest league)
                const currentIndex = LEAGUES.indexOf(currentLeague);
                if (currentIndex > 0) {
                    newLeague = LEAGUES[currentIndex - 1];
                }
            }
        } else if (!user.league) {
            // New users start in Bronzana
            newLeague = 'Bronzana';
        }

        assignments[newLeague].push({
            id: userId,
            username: user.uniqueName || 'Unknown',
            previousLeague: currentLeague,
            newLeague: newLeague,
            previousPosition: userStanding?.position || null,
            weeklyScore: user.weeklyScore || 0
        });
    }

    return assignments;
}

// Helper function to clear existing leaderboard groups
async function clearLeaderboardGroups(firestore) {
    const batch = firestore.batch();

    for (const league of LEAGUES) {
        try {
            const groupsSnapshot = await firestore
                .collection('leaderboards')
                .doc(league)
                .collection('groups')
                .get();

            for (const groupDoc of groupsSnapshot.docs) {
                const playersSnapshot = await groupDoc.ref.collection('players').get();

                // Delete all players in the group
                for (const playerDoc of playersSnapshot.docs) {
                    batch.delete(playerDoc.ref);
                }

                // Delete the group document
                batch.delete(groupDoc.ref);
            }
        } catch (error) {
            logger.error(`Error clearing groups for ${league}:`, error);
        }
    }

    await batch.commit();
    logger.info('Cleared all existing leaderboard groups');
}

// Helper function to update user league assignments
async function updateUserLeagueAssignments(firestore, leagueAssignments) {
    const batch = firestore.batch();

    for (const league of LEAGUES) {
        const players = leagueAssignments[league] || [];

        for (const player of players) {
            const userRef = firestore.collection('users').doc(player.id);
            batch.update(userRef, {
                league: player.newLeague,
                previousLeague: player.previousLeague,
                lastLeagueUpdate: admin.firestore.Timestamp.now()
            });
        }
    }

    await batch.commit();
    logger.info('Updated user league assignments');
}

const ACHIEVEMENTS = {
    'Srebrna': { id: 'silver_league_achiever', neurons: 500 },
    'Zlatna': { id: 'gold_league_achiever', neurons: 1000 },
};

const TITLES = [
    { name: 'Početnik', xpRequired: 3000 },
    { name: 'Pripravnik', xpRequired: 6000 },
    { name: 'Vešt', xpRequired: 10000 },
    { name: 'Stručnjak', xpRequired: 15000 },
    { name: 'Specijalista', xpRequired: 20000 },
    { name: 'Ekspert', xpRequired: 30000 },
    { name: 'Veteran', xpRequired: 40000 },
    { name: 'Majstor', xpRequired: 50000 },
    { name: 'Virtuoz', xpRequired: 60000 },
    { name: 'Mudrac', xpRequired: 70000 },
    { name: 'Velemajstor', xpRequired: 80000 },
    { name: 'Superstar', xpRequired: 90000 },
];



// Function to generate a unique username based on name only
async function generateUniqueUsername(firestore, uid, name) {
    const usersRef = firestore.collection('users');
    const baseName = name.toLowerCase().replaceAll(' ', '');

    let suffix = 1;
    let uniqueUsername;

    while (true) {
        uniqueUsername = `@${baseName}-${suffix}`;
        const existing = await usersRef.where('uniqueName', 'isEqualTo', uniqueUsername).get();

        if (existing.docs.length === 0) {
            break;
        }
        suffix++;
    }

    // Update the user's document with the unique username
    await usersRef.doc(uid).update({
        uniqueName: uniqueUsername,
        updatedAt: admin.firestore.Timestamp.now(),
    });

    logger.info(`Generated unique username ${uniqueUsername} for user ${uid}`);
    return uniqueUsername;
}


// Weekly leaderboard update (runs every Monday at 00:01)
exports.weeklyLeaderboardUpdate = onSchedule('1 0 * * 1', async (event) => {
    try {
        logger.info('Starting weekly leaderboard update');
        await updateLeaderboard();
        logger.info('Weekly leaderboard update completed successfully');
    } catch (error) {
        logger.error('Error in weekly leaderboard update:', error);
    }
});

// Manual leaderboard update
exports.manualUpdateLeaderboard = onCall(async (data, context) => {
    try {
        logger.info('Starting manualUpdateLeaderboard');
        await updateLeaderboard();
        logger.info('manualUpdateLeaderboard completed successfully');
        return { status: 'success', message: 'Leaderboard manually updated successfully.' };
    } catch (error) {
        logger.error('Error in manualUpdateLeaderboard:', error);
        throw new Error(`Failed to update leaderboard: ${error.message}`);
    }
});
async function updateLeaderboard() {
    const firestore = admin.firestore();
    logger.info('Starting updateLeaderboard');

    // Step 1: Reset weekly scores and preserve historical data
    logger.info('Resetting weekly scores for all users');
    const currentWeekStart = getCurrentWeekStart();
    const usersSnapshot = await firestore.collection('users').get();

    const batch = firestore.batch();
    for (const userDoc of usersSnapshot.docs) {
        const userRef = userDoc.ref;
        const userData = userDoc.data();

        // Store current week's score as historical data
        const weeklyHistoryRef = userRef.collection('weeklyHistory').doc(currentWeekStart.toISOString());
        batch.set(weeklyHistoryRef, {
            weekStart: admin.firestore.Timestamp.fromDate(currentWeekStart),
            weeklyScore: userData.weeklyScore || 0,
            league: userData.league || 'Bronzana',
            totalNeurons: userData.neurons || 0
        });

        // Reset weekly score for new week
        batch.update(userRef, {
            weeklyScore: 0,
            lastWeeklyReset: admin.firestore.Timestamp.fromDate(currentWeekStart)
        });
    }
    await batch.commit();

    // Step 2: Fetch users with updated data
    logger.info('Fetching users from users collection');
    const updatedUsersSnapshot = await firestore.collection('users').get();
    const users = updatedUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    logger.info(`Fetched ${users.length} users`);

  // Step 2: Ensure all users have a unique username
    logger.info('Ensuring unique usernames for all users');
    for (let user of users) {
        if (!user.uniqueName || user.uniqueName === 'Unknown') {
            user.uniqueName = await generateUniqueUsername(
                firestore,
                user.uid,
                user.name, 
            );
        }
    }

    // Step 3: Get previous week's league standings for promotion/demotion
    logger.info('Getting previous week league standings for promotion/demotion');
    const previousWeekStart = getPreviousWeekStart();
    const leagueStandings = await getPreviousWeekStandings(firestore, previousWeekStart);

    // Step 4: Calculate league assignments with proper promotion/demotion
    logger.info('Calculating league assignments with promotion/demotion rules');
    const leagueAssignments = await calculateLeagueAssignments(firestore, users, leagueStandings);

    // Step 4: Promote/Demote in Transaction
    await firestore.runTransaction(async (transaction) => {
        for (const league of LEAGUES) {
            const groupsSnapshot = await firestore
                .collection('leaderboards')
                .doc(league)
                .collection('groups')
                .get();
            for (const groupDoc of groupsSnapshot.docs) {
                const playersSnapshot = await groupDoc.ref.collection('players')
                    .orderBy('score', 'desc')
                    .orderBy('lastUpdated', 'asc')
                    .get();
                const players = playersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                logger.info(`Processing ${players.length} players in ${league}/${groupDoc.id}`);
                if (players.length === 0) continue;

                const activePlayers = players.filter(player => (player.score || 0) > 0);
                const inactivePlayers = players.filter(player => (player.score || 0) === 0);

                // FIXED: Better promotion/demotion logic for small groups
                let top3 = [];
                let bottom3 = [];

                if (activePlayers.length >= 6) {
                    // Normal case: take top 3 and bottom 3
                    top3 = activePlayers.slice(0, 3);
                    bottom3 = activePlayers.slice(-3);
                } else if (activePlayers.length >= 4) {
                    // Small group: take top 1 and bottom 1
                    top3 = activePlayers.slice(0, 1);
                    bottom3 = activePlayers.slice(-1);
                } else if (activePlayers.length >= 2) {
                    // Very small group: only promote top 1, no demotion
                    top3 = activePlayers.slice(0, 1);
                    bottom3 = [];
                } else {
                    // Single player or no active players: no promotion/demotion
                    top3 = [];
                    bottom3 = [];
                }

                // Promote players
                if (league !== 'Elitna') {
                    for (const player of top3) {
                        if (player.score > 0) {
                            await promotePlayer(player, league, groupDoc.id, LEAGUES, firestore, transaction);
                        }
                    }
                }

                // Demote players (but not if it would leave the group empty)
                if (league !== 'Bronzana') {
                    const remainingAfterPromotion = players.length - top3.length;
                    const playersToDemote = [];
                    
                    // Only demote if there will be at least 1 player left in the group
                    if (remainingAfterPromotion > bottom3.length) {
                        playersToDemote.push(...bottom3);
                    }
                    
                    // Always demote inactive players if there are active players left
                    if (activePlayers.length > 0) {
                        playersToDemote.push(...inactivePlayers);
                    }

                    for (const player of playersToDemote) {
                        if (!top3.some(topPlayer => topPlayer.id === player.id)) {
                            await demotePlayer(player, league, groupDoc.id, LEAGUES, firestore, transaction);
                        }
                    }
                }

                // Clean up old group structure
                for (const player of players) {
                    const playerRef = groupDoc.ref.collection('players').doc(player.id);
                    transaction.delete(playerRef);
                }
                if (players.length > 0) {
                    transaction.delete(groupDoc.ref);
                }
            }
        }
    });



    // Step 5: Reassignment
    logger.info('Starting group reassignment');
    const updatedGroups = {};
    LEAGUES.forEach(league => { updatedGroups[league] = []; });

    const finalUsersSnapshot = await firestore.collection('users').get();
    const updatedUsers = finalUsersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    logger.info(`Fetched ${updatedUsers.length} users for group reassignment`);

    for (const user of updatedUsers) {
        // Ensure unique username during reassignment
        let username = user.uniqueName;
        if (!username || username === 'Unknown') {
            username = await generateUniqueUsername(
                firestore,
                user.uid,
                user.name,
            );
        }

        const league = user.league || 'Bronzana';
        logger.info(`User ${user.id} has league: ${league}`);
        updatedGroups[league].push({
            id: user.id,
            username: username,
            league: league,
            score: 0,
            playerId: user.id
        });
    }

    for (const league of LEAGUES) {
        const leaguePlayers = updatedGroups[league];
        logger.info(`Assigning ${leaguePlayers.length} players in ${league}`);

        if (leaguePlayers.length === 0) {
            continue;
        }

        // Sort players by ID for consistent grouping
        leaguePlayers.sort((a, b) => a.id.localeCompare(b.id));

        // FIXED: Better group distribution algorithm
        const totalPlayers = leaguePlayers.length;
        const minGroupSize = 5; // Minimum players per group
        const maxGroupSize = 10; // Maximum players per group
        
        let numGroups;
        if (totalPlayers <= maxGroupSize) {
            // All players in one group
            numGroups = 1;
        } else {
            // Calculate optimal number of groups
            numGroups = Math.ceil(totalPlayers / maxGroupSize);
            
            // Ensure no group is too small
            while (totalPlayers / numGroups < minGroupSize && numGroups > 1) {
                numGroups--;
            }
        }

        const playersPerGroup = Math.floor(totalPlayers / numGroups);
        const extraPlayers = totalPlayers % numGroups;

        let currentIndex = 0;
        for (let groupIndex = 1; groupIndex <= numGroups; groupIndex++) {
            const groupSize = playersPerGroup + (groupIndex <= extraPlayers ? 1 : 0);
            const groupPlayers = leaguePlayers.slice(currentIndex, currentIndex + groupSize);
            
            await assignGroup(firestore, league, `group_${groupIndex}`, groupPlayers);
            logger.info(`Assigned group_${groupIndex} in ${league} with ${groupPlayers.length} players`);
            
            currentIndex += groupSize;
        }
    }

    logger.info('updateLeaderboard completed');
}
    async function assignGroup(firestore, league, groupId, players) {
    const batch = firestore.batch();
    const groupRef = firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .doc(groupId);

    batch.set(groupRef, {
        createdAt: admin.firestore.Timestamp.now(),
        league: league,
        groupId: groupId
    });

    for (const player of players) {
        const playerRef = groupRef.collection('players').doc(player.id);
        batch.set(playerRef, {
            username: player.username || 'Unknown',
            league: league,
            score: player.score || 0,
            playerId: player.id,
            lastUpdated: admin.firestore.Timestamp.now(),
        });
        batch.update(firestore.collection('users').doc(player.id), {
            groupId: groupId,
            league: league, // Ensure league is properly set
        });
    }

    await batch.commit();
    logger.info(`Assigned ${players.length} players to ${league}/${groupId}`);

    // Verify league field after assignment
    for (const player of players) {
        const userDoc = await firestore.collection('users').doc(player.id).get();
        logger.info(`Post-assignment league for ${player.id}: ${userDoc.data().league}`);
    }
}
async function promotePlayer(player, currentLeague, groupId, leagueOrder, firestore, transaction) {
    const nextLeague = leagueOrder[leagueOrder.indexOf(currentLeague) + 1];
    if (!nextLeague) {
        logger.info(`No next league for ${currentLeague}, skipping promotion for ${player.playerId}`);
        return;
    }

    const userRef = firestore.collection('users').doc(player.playerId);
    logger.info(`Promoting player ${player.playerId} from ${currentLeague} to ${nextLeague}`);

    transaction.update(userRef, {
        league: nextLeague,
        leagueEventsCounter: admin.firestore.FieldValue.increment(1),
        lastPromotion: admin.firestore.Timestamp.now(),
        // Flag to check league achievements on next app launch
        shouldCheckLeagueAchievements: true,
    });

    const userDoc = await userRef.get();
    if (!userDoc.exists) {
        logger.warn(`User ${player.playerId} not found for notification`);
        return;
    }
    const deviceToken = userDoc.data().deviceToken;
    if (deviceToken) {
        await sendNotification(deviceToken, 'Čestitamo!', `Promovisani ste u ${nextLeague}!`);
    }

    logger.info(`Player ${player.playerId} promoted from ${currentLeague} to ${nextLeague}`);
}

async function demotePlayer(player, currentLeague, groupId, leagueOrder, firestore, transaction) {
    const prevLeague = leagueOrder[leagueOrder.indexOf(currentLeague) - 1];
    if (!prevLeague) {
        logger.info(`No previous league for ${currentLeague}, skipping demotion for ${player.playerId}`);
        return;
    }

    const userRef = firestore.collection('users').doc(player.playerId);
    logger.info(`Demoting player ${player.playerId} from ${currentLeague} to ${prevLeague}`);

    transaction.update(userRef, {
        league: prevLeague,
        leagueEventsCounter: admin.firestore.FieldValue.increment(1),
        lastDemotion: admin.firestore.Timestamp.now(),
    });

    const notificationRef = firestore
        .collection('notifications')
        .doc(player.playerId)
        .collection('messages')
        .doc();
    transaction.set(notificationRef, {
        message: `Nažalost, vraćeni ste u ${prevLeague}.`,
        timestamp: admin.firestore.Timestamp.now(),
    });

    const userDoc = await userRef.get();
    if (!userDoc.exists) {
        logger.warn(`User ${player.playerId} not found for notification`);
        return;
    }
    const deviceToken = userDoc.data().deviceToken;
    if (deviceToken) {
        await sendNotification(deviceToken, 'Nažalost!', `Vraćeni ste u ${prevLeague}.`);
    }

    logger.info(`Player ${player.playerId} demoted from ${currentLeague} to ${prevLeague}`);
}
async function sendNotification(token, title, body) {
    if (!token) {
        logger.info('No device token provided, skipping notification');
        return;
    }

    const message = {
        notification: { title, body },
        token
    };

    try {
        await admin.messaging().send(message);
        logger.info(`Notification sent to ${token}`);
    } catch (error) {
        logger.error(`Failed to send notification to ${token}:`, error);
    }
}


// // 🔥 TEMP: Run only when this file is executed directly (not in production)
// if (require.main === module) {
//   updateLeaderboard()
//     .then(() => {
//       console.log('✅ Leaderboard updated manually (local)');
//       process.exit(0);
//     })
//     .catch((err) => {
//       console.error('❌ Error updating leaderboard:', err);
//       process.exit(1);
//     });
// }
