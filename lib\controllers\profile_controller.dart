import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/info_sheet.dart';
import 'package:flutter/material.dart';
import '../models/lesson_model.dart';
import '../models/quiz_model.dart';
import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/models/user_model.dart';
import 'package:bibl/services/lessonquiz_completion_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:image_picker/image_picker.dart';

import '../services/reward_service.dart';
import '../widgets/rate_app_dialog.dart';

class ProfileController extends GetxController with WidgetsBindingObserver {
  Rx<bool> isProfilePhotoUpdating = false.obs;
  Rx<bool> isProfileUpdating = false.obs;
  Rx<bool> isUserDataLoading = false.obs;
  Rx<UserModel> userr = UserModel(isPremiumUser: false).obs;

  DateTime? _startTime;
  DateTime? _endTime;

  Rx<String> name = ''.obs;

  Rx<bool> haveSeenPaywall = false.obs;
  Rx<bool> isNotificationOn = true.obs;
  Rx<bool> isSoundOn = true.obs;
  Rx<bool> isAdLoaded = false.obs;
  Rx<InterstitialAd?> rewardeInterstitialdAd = Rx<InterstitialAd?>(null);
// Suppose these are reactive lists:
  // Example for Lessons, Quizzes, and ShuffleQuizzes
  RxList<LessonModel> savedLessons = <LessonModel>[].obs;
  RxList<QuizModel> savedQuizzes = <QuizModel>[].obs;
  RxList<ShuffleQuizModel> savedShuffleQuizzes = <ShuffleQuizModel>[].obs;

  Rx<bool> isPremiumUser = false.obs;

  Future<void> updateUserData() async {
    await getUserData();
  }

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    updateUserData();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this); // Remove observer on close
    super.onClose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // App resumed, start tracking
      _startTime = DateTime.now();
    } else if (state == AppLifecycleState.paused) {
      // App paused, stop tracking
      _endTime = DateTime.now();
      logTimeSpent();
    }
  }

  void _listenToSavedLessons(String uid) {
    firestore
        .collection('users')
        .doc(uid)
        .collection('savedLessons')
        .snapshots()
        .listen((snapshot) {
      // Convert Firestore docs to LessonModel
      final newList =
          snapshot.docs.map((doc) => LessonModel.fromSnap(doc)).toList();

      // Assign the new list to the reactive variable
      savedLessons.assignAll(newList);
    });
  }

  void _listenToSavedQuizzes(String uid) {
    firestore
        .collection('users')
        .doc(uid)
        .collection('savedQuizzes')
        .snapshots()
        .listen((snapshot) {
      final newList =
          snapshot.docs.map((doc) => QuizModel.fromSnap(doc)).toList();
      savedQuizzes.assignAll(newList);
    });
  }

  void _listenToSavedShuffleQuizzes(String uid) {
    firestore
        .collection('users')
        .doc(uid)
        .collection('savedShuffleQuizzes')
        .snapshots()
        .listen((snapshot) {
      final newList =
          snapshot.docs.map((doc) => ShuffleQuizModel.fromSnap(doc)).toList();
      savedShuffleQuizzes.assignAll(newList);
    });
  }

  Future<void> addToSavedLesson({
    required LessonModel savedLesson,
  }) async {
    // Reference to the document in the collection
    final lessonDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('savedLessons')
        .doc(savedLesson.lessonId);

    // Check if the document already exists
    final docSnapshot = await lessonDocReference.get();

    if (docSnapshot.exists) {
      // If it exists, update the existing document
      await lessonDocReference.update(
        savedLesson.toJson(),
      );
    } else {
      // If it doesn't exist, create a new document
      await lessonDocReference.set(savedLesson.toJson());
    }
  }

  Future<void> deleteFromSavedLesson({
    required String savedLessonId,
  }) async {
    final savedLessonDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('savedLessons')
        .doc(savedLessonId);

    // Check if the document already exists
    final savedLessonDocSnapshot = await savedLessonDocReference.get();

    if (savedLessonDocSnapshot.exists) {
      // If it exists, delete the document
      await savedLessonDocReference.delete();
    }
  }

  Future<void> addToSavedQuizes({
    required QuizModel savedQuiz,
  }) async {
    // Reference to the document in the collection
    final quizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('savedQuizzes')
        .doc(savedQuiz.quizId);

    // Check if the document already exists
    final docSnapshot = await quizDocReference.get();

    if (docSnapshot.exists) {
      // If it exists, update the existing document
      await quizDocReference.set(
        savedQuiz.toJson(),
        SetOptions(merge: true),
      );
    } else {
      // If it doesn't exist, create a new document
      await quizDocReference.set(savedQuiz.toJson());
    }
  }

  Future<void> deleteFromSavedQuizes({
    required String savedQuizId,
  }) async {
    final savedQuizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('savedQuizzes')
        .doc(savedQuizId);

    // Check if the document already exists
    final savedQuizDocSnapshot = await savedQuizDocReference.get();

    if (savedQuizDocSnapshot.exists) {
      // If it exists, delete the document
      await savedQuizDocReference.delete();
    }
  }

  Future<void> addToSavedShuffleQuizes({
    required ShuffleQuizModel savedQuiz,
  }) async {
    // Reference to the document in the collection
    final quizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('savedShuffleQuizzes')
        .doc(savedQuiz.quizId);

    // Check if the document already exists
    final docSnapshot = await quizDocReference.get();

    if (docSnapshot.exists) {
      // If it exists, update the existing document
      await quizDocReference.set(
        savedQuiz.toJson(),
        SetOptions(merge: true),
      );
    } else {
      // If it doesn't exist, create a new document
      await quizDocReference.set(savedQuiz.toJson());
    }
  }

  Future<void> deleteFromSavedShuffleQuizes({
    required String savedQuizId,
  }) async {
    final savedQuizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('savedShuffleQuizzes')
        .doc(savedQuizId);

    // Check if the document already exists
    final savedQuizDocSnapshot = await savedQuizDocReference.get();

    if (savedQuizDocSnapshot.exists) {
      // If it exists, delete the document
      await savedQuizDocReference.delete();
    }
  }

  Future<void> logTimeSpent() async {
    if (_startTime != null && _endTime != null) {
      int timeSpent = _endTime!.difference(_startTime!).inMinutes;

      // Update Firestore with the time spent
      await firestore.collection('users').doc(userr.value.uid).update({
        'totalTimeSpent': FieldValue.increment(timeSpent),
      });

      // Reset start and end times
      _startTime = null;
      _endTime = null;
    }
  }

  updateDeviceToken(String userId) async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;

    // Get the device token
    String? token = await messaging.getToken();

    // Update the device token in Firestore
    firestore.collection('users').doc(userId).update({
      'deviceToken': token,
    });
  }

  void checkRankedFirst(String userId) async {
    RewardService rewardService = RewardService(userId);
    final userDoc = await firestore.collection('users').doc(userId).get();

    if (userDoc.exists && userDoc.data()?['rankedFirst'] == true) {
      rewardService.quizKingQueen();
      // Update `rankedFirst` to false
      await firestore.collection('users').doc(userId).update({
        'rankedFirst': false,
      });
    }
  }

  checkUserLeagueOnAppLaunch(String userId) async {
    // League achievements are now handled automatically in RewardService
    // when leaderboard is updated, so we don't need to check here
    // This prevents duplicate achievement dialogs
  }

  setSoundStatus(bool value) async {
    if (value) {
      isSoundOn.value = true;
    } else {
      isSoundOn.value = false;
    }

    await soundUpdate();
  }

  setNotificationStatus(bool value) async {
    if (value) {
      isNotificationOn.value = true;
    } else {
      isNotificationOn.value = false;
    }

    await notificationUpdate();
  }

  updateStreak(int streak) async {
    if (userr.value.uid != null) {
      DocumentReference userDoc =
          firestore.collection('users').doc(userr.value.uid);

      DocumentSnapshot docSnapshot = await userDoc.get(); // Check if doc exists

      if (docSnapshot.exists) {
        await userDoc.update({
          'weeklyStreak': streak,
          'lastStreakUpdate': Timestamp.now(),
        });
      }

      // Update local data
      userr.value.weeklyStreak = streak;
      userr.value.lastStreakUpdate = Timestamp.now();
      userr.refresh();
    }
  }

  updateConsecutiveStreak(int streak) async {
    if (userr.value.uid != null) {
      DocumentReference userDoc =
          firestore.collection('users').doc(userr.value.uid);

      DocumentSnapshot docSnapshot = await userDoc.get(); // Check if doc exists

      if (docSnapshot.exists) {
        await userDoc.update({
          'consecutiveStreak': streak,
        });
      }
    }
  }

  Future<void> updateProfile(
      {required String name,
      required String surname,
      required String currentPassword,
      required String newPassword}) async {
    try {
      isProfileUpdating.value = true;
      Map<String, dynamic> dataToUpdate = {};

// Add fields to the update map only if they are not empty
      if (name.isNotEmpty) {
        dataToUpdate['name'] = name;
        userr.value.name = name;
      }

      if (surname.isNotEmpty) {
        dataToUpdate['surname'] = surname;
        userr.value.surname = surname;
      }

// Update Firestore only if there are fields to update
      if (dataToUpdate.isNotEmpty) {
        await firestore
            .collection('users')
            .doc(userr.value.uid)
            .update(dataToUpdate);
      }

      if (currentPassword.isNotEmpty && newPassword.isNotEmpty) {
        await changePassword(
            currentPassword: currentPassword, newPassword: newPassword);
      }
    } finally {
      isProfileUpdating.value = false;
      userr.refresh();
    }
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final user = FirebaseAuth.instance.currentUser;

    if (user != null) {
      try {
        // 1. Reauthenticate the user
        AuthCredential credential = EmailAuthProvider.credential(
          email: user.email!,
          password: currentPassword,
        );
        print('Email: ${user.email}');
        print('Current Password: $currentPassword');

        // await user.reauthenticateWithCredential(credential);
        if (user.providerData[0].providerId == 'google.com') {
          print('its google');
          await user.reauthenticateWithProvider(GoogleAuthProvider());
        } else if (user.providerData[0].providerId == 'apple.com') {
          print('its apple');
          await user.reauthenticateWithProvider(AppleAuthProvider());
        } else {
          await user.reauthenticateWithCredential(credential);
        }
        await user.updatePassword(newPassword);

        // Show success message or navigate the user back
      } catch (e) {
        print('Error changing password: $e');
        // Handle errors, e.g., wrong password, weak new password
        getErrorSnackBar('Nije uspelo menjanje lozinke, pokušajte ponovo');
      }
    } else {}
  }

  // Update profile photo and sync with Firestore
  Future<void> updateProfilePhoto(String photoPath) async {
    try {
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        return;
      }

      // Update Firestore
      await FirebaseFirestore.instance.collection('users').doc(userId).update(
        {
          'profilePhoto': photoPath,
        },
      );

      // Update local user model
      userr.update((user) {
        user?.profilePhoto = photoPath;
      });
    } catch (e) {
      //
    }
  }

  Future<void> getUserData() async {
    final AuthController authController = Get.find();
    final LessonController lessonController = Get.find();
    final LessonQuizCompletionService completionService =
        LessonQuizCompletionService();

    try {
      dynamic userDoc = await firestore
          .collection('users')
          .doc(authController.user!.uid)
          .get();

      userr.value = UserModel.fromSnap(userDoc);
      isNotificationOn.value = userr.value.isNotificationOn ?? false;
      isSoundOn.value = userr.value.isSoundOn ?? false;

      // Ensure the UI updates immediately
      userr.refresh();

      // UserModel usserr = UserModel.fromSnap(userDoc);

      if (userr.value.uid != null) {
        if ((userr.value.hasSeenInfoSheet ?? false) == false) {
          Get.bottomSheet(
            const InfoSheet(),
            isScrollControlled: true,
          );
        }

        if (lessonController.homeDisplayedItems.isEmpty) {
          lessonController.mergeAndShuffleItems(
              isShuffle: true, from: 'prof controller', shouldClear: false);
        }
        if (lessonController.libraryDisplayedItems.isEmpty) {
          lessonController.shuffleAllItems(
            isShuffle: true,
            shouldClear: false,
            from: 'prof controller all',
          );
        }

        if (userr.value.uniqueName == null || userr.value.uniqueName!.isEmpty) {
          generateUniqueUsername(userr.value.uid!, userr.value.name!);
        }
        final photo = userr.value.profilePhoto;
        final allowedPhotos = [
          'assets/images/img1.png',
          'assets/images/img2.png',
          'assets/images/img3.png',
          'assets/images/img4.png',
        ];

        if (photo == null || photo.isEmpty || !allowedPhotos.contains(photo)) {
          addAvatarImg(userr.value.uid!);
        }

        _listenToSavedLessons(userr.value.uid!);
        _listenToSavedQuizzes(userr.value.uid!);
        _listenToSavedShuffleQuizzes(userr.value.uid!);

        updateLastActiveTime(userr.value.uid!);
        checkUserLeagueOnAppLaunch(userr.value.uid!);
        completionService.cleanExpiredLessonsQuizes(userr.value.uid!);
        updateDeviceToken(userr.value.uid!);
        checkAndAwardNeurons(userr.value.uid!);
        checkRankedFirst(userr.value.uid!);
        checkCompetitiveSpiritAchievement(userr.value.uid!);
        checkTenLeagueEvents(userr.value.uid!);
      }
      _checkFeedbackPopupConditions();
    } catch (e) {
      // Handle error
    } finally {
      isUserDataLoading.value = true;
    }
  }

  void _checkFeedbackPopupConditions() {
    print('_checkFeedbackPopupConditions called');
    // Increment the app open count
    userr.value.appOpenCount = (userr.value.appOpenCount ?? 0) + 1;

    // Check if it's the second time opening the app
    if (userr.value.appOpenCount == 2) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.dialog(
          const RateAppDialog(),
        );
      });
      userr.value.lastPopupShownTimestamp = Timestamp.now();
    } else if (userr.value.lastPopupShownTimestamp != null) {
      // Check if 90 days have passed since the last popup
      final now = DateTime.now();
      final lastPopupDate = userr.value.lastPopupShownTimestamp!.toDate();
      final difference = now.difference(lastPopupDate).inDays;

      if (difference >= 90) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.dialog(
            const RateAppDialog(),
          );
        });
        userr.value.lastPopupShownTimestamp = Timestamp.now();
      }
    }

    // Save the updated user model to Firestore
    updatelastPopupShownTimestampandAppCounts();
  }

  updateHearts(int hearts) async {
    if (userr.value.uid != null) {
      DocumentReference userDoc =
          firestore.collection('users').doc(userr.value.uid);

      DocumentSnapshot docSnapshot = await userDoc.get(); // Check if doc exists

      if (docSnapshot.exists) {
        await userDoc.update({'hearts': hearts});
      }
    }
  }

  updatelastPopupShownTimestampandAppCounts() async {
    await firestore.collection('users').doc(userr.value.uid).update({
      'lastPopupShownTimestamp': userr.value.lastPopupShownTimestamp,
      'appOpenCount': userr.value.appOpenCount,
    });
  }

  updatedOpenedQuizesAndArticles(String itemID) async {
    final completionService = LessonQuizCompletionService();

    // Check if already opened within 30 days
    if (!completionService.isOpenedWithinThirtyDays(
        itemID, userr.value.openedQuizesAndArticlesinMonth)) {
      await completionService.addOpenedQuizArticleAndUpdateFirestore(
          userr.value.uid!, itemID);
    }
  }

  updatedOpenedArticlesCount(String itemID) async {
    final completionService = LessonQuizCompletionService();

    // Check if already opened within 30 days
    if (!completionService.isOpenedWithinThirtyDays(
        itemID, userr.value.openedQuizesAndArticlesinMonth)) {
      await completionService.addOpenedQuizArticleAndUpdateFirestore(
          userr.value.uid!, itemID);
    }

    // Also update the opened articles list for achievements
    final openedArticleIds = userr.value.openedArticleIds ?? [];
    if (!openedArticleIds.contains(itemID)) {
      final updatedOpenedIds = [...openedArticleIds, itemID];

      await firestore.collection('users').doc(userr.value.uid).update({
        'openedArticleIds': FieldValue.arrayUnion([itemID]),
      });

      userr.value.openedArticleIds = updatedOpenedIds;
      userr.refresh();
    }
  }

  checkTenLeagueEvents(String userId) {
    RewardService rewardService = RewardService(userId);
    if (userr.value.leagueEventsCounter == 10) {
      rewardService.participationTrophy();
    }
  }

  /// Method to check and reward the "Competitive Spirit" achievement
  Future<void> checkCompetitiveSpiritAchievement(String userId) async {
    RewardService rewardService = RewardService(userId);

    // Check leagueWinsCount and achievements
    final leagueWinsCount = userr.value.leagueWinsCount ?? 0;
    final achievements = userr.value.achievements ?? [];

    if (leagueWinsCount > 5 && !achievements.contains('competitive_spirit')) {
      // Reward the user
      await rewardService.competitiveSpirit();
    }
  }

  void checkAndAwardNeurons(String userId) {
    RewardService rewardService = RewardService(userId);
    int totalTime = userr.value.totalTimeSpent ?? 0; // User's total time spent
    int lastMilestone =
        userr.value.lastMilestone ?? 0; // Last milestone reached

    // Check if the user has crossed a new 1000-minute milestone
    if (totalTime >= lastMilestone + 1000) {
      // Award 1000 neurons
      userr.value.neurons = (userr.value.neurons ?? 0) + 1000;

      // Update the last milestone to the current milestone
      userr.value.lastMilestone = lastMilestone + 1000;
      rewardService.powerUser();

      // Save the updated user data to Firestore or your database
      updateMilestoneInFirestore(
          userr.value.lastMilestone!); // Assume this function updates Firestore
    }
  }

  void updateMilestoneInFirestore(int lastMilestone) async {
    await firestore.collection('users').doc(userr.value.uid).update({
      'lastMilestone': lastMilestone,
    });
  }

  Future<void> updateLastActiveTime(String userId) async {
    await firestore.collection('users').doc(userId).update({
      'lastActiveTime': DateTime.now(),
    });
  }

  Future<void> generateUniqueUsername(String uid, String name) async {
    final usersRef = FirebaseFirestore.instance.collection('users');
    final baseName = name.toLowerCase().replaceAll(' ', '');

    int suffix = 1;
    String uniqueUsername;

    while (true) {
      uniqueUsername = '@$baseName-$suffix';

      final existing =
          await usersRef.where('uniqueName', isEqualTo: uniqueUsername).get();

      if (existing.docs.isEmpty) {
        break;
      }

      suffix++;
    }

    // Save to Firestore
    await usersRef.doc(uid).update({
      'uniqueName': uniqueUsername,
    });

    // Update locally if needed
    userr.value.uniqueName = uniqueUsername;
    userr.refresh();
  }

  Future<void> addAvatarImg(String uid) async {
    List<String> imgList = [
      'assets/images/img1.png',
      'assets/images/img2.png',
      'assets/images/img3.png',
      'assets/images/img4.png',
    ];
    Random random = Random();
    String randomImage = imgList[random.nextInt(imgList.length)];

    final usersRef = FirebaseFirestore.instance.collection('users');

    // Save to Firestore
    await usersRef.doc(uid).update({
      'profilePhoto': randomImage,
    });

    // Update locally if needed
    userr.value.profilePhoto = randomImage;
    userr.refresh();
  }

  Future<void> userTitleUpdate(int neurons) async {
    String newTitle = '';

    // Determine title based on the number of neurons (experience points)
    if (neurons < 3000) {
      newTitle = 'Početnik'; // Newcomer
    } else if (neurons < 6000) {
      newTitle = 'Pripravnik'; // Apprentice
    } else if (neurons < 10000) {
      newTitle = 'Vešt'; // Adept
    } else if (neurons < 15000) {
      newTitle = 'Stručnjak'; // Proficient
    } else if (neurons < 20000) {
      newTitle = 'Specijalista'; // Specialist
    } else if (neurons < 30000) {
      newTitle = 'Ekspert'; // Expert
    } else if (neurons < 40000) {
      newTitle = 'Veteran'; // Veteran
    } else if (neurons < 50000) {
      newTitle = 'Majstor'; // Master
    } else if (neurons < 60000) {
      newTitle = 'Virtuoz'; // Virtuoso
    } else if (neurons < 70000) {
      newTitle = 'Mudrac'; // Sage
    } else if (neurons < 80000) {
      newTitle = 'Velemajstor'; // Grandmaster
    } else if (neurons < 90000) {
      newTitle = 'Superstar'; // Luminary
    }

    // Update the user's title in Firestore
    await firestore.collection('users').doc(userr.value.uid).update({
      'title': newTitle,
    });
  }

  Future<void> updateUserSubscription() async {
    await firestore.collection('users').doc(userr.value.uid).update({
      'isPremiumUser': isPremiumUser.value,
    });
  }

  Future<void> notificationUpdate() async {
    DocumentReference userDoc =
        firestore.collection('users').doc(userr.value.uid);

    DocumentSnapshot docSnapshot = await userDoc.get();
    if (docSnapshot.exists) {
      await userDoc.update({'isNotificationOn': isNotificationOn.value});
    }
  }

  Future<void> soundUpdate() async {
    DocumentReference userDoc =
        firestore.collection('users').doc(userr.value.uid);

    DocumentSnapshot docSnapshot = await userDoc.get();
    if (docSnapshot.exists) {
      await userDoc.update({'isSoundOn': isSoundOn.value});
    }
  }

  Future<void> addToFavLesson({
    required LessonModel favLesson,
  }) async {
    // Reference to the document in the collection
    final lessonDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('favLessons')
        .doc(favLesson.lessonId);

    // Check if the document already exists
    final docSnapshot = await lessonDocReference.get();

    if (docSnapshot.exists) {
      // If it exists, update the existing document
      await lessonDocReference.update(
        favLesson.toJson(),
      );
    } else {
      // If it doesn't exist, create a new document
      await lessonDocReference.set(favLesson.toJson());
    }
  }

  Future<void> deleteFromFavLesson({
    required String favLessonId,
  }) async {
    final favLessonDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('favLessons')
        .doc(favLessonId);

    // Check if the document already exists
    final favLessonDocSnapshot = await favLessonDocReference.get();

    if (favLessonDocSnapshot.exists) {
      // If it exists, delete the document
      await favLessonDocReference.delete();
    }
  }

  Future<void> addToFavQuizes({
    required QuizModel favQuiz,
  }) async {
    // Reference to the document in the collection
    final quizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('favQuizzes')
        .doc(favQuiz.quizId);

    // Check if the document already exists
    final docSnapshot = await quizDocReference.get();

    if (docSnapshot.exists) {
      // If it exists, update the existing document
      await quizDocReference.set(
        favQuiz.toJson(),
        SetOptions(merge: true),
      );
    } else {
      // If it doesn't exist, create a new document
      await quizDocReference.set(favQuiz.toJson());
    }
  }

  Future<void> deleteFromFavQuizes({
    required String favQuizId,
  }) async {
    final favQuizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('favQuizzes')
        .doc(favQuizId);

    // Check if the document already exists
    final favQuizDocSnapshot = await favQuizDocReference.get();

    if (favQuizDocSnapshot.exists) {
      // If it exists, delete the document
      await favQuizDocReference.delete();
    }
  }

  Future<void> addToFavShuffleQuizes({
    required ShuffleQuizModel favQuiz,
  }) async {
    // Reference to the document in the collection
    final quizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('favShuffleQuizzes')
        .doc(favQuiz.quizId);

    // Check if the document already exists
    final docSnapshot = await quizDocReference.get();

    if (docSnapshot.exists) {
      // If it exists, update the existing document
      await quizDocReference.set(
        favQuiz.toJson(),
        SetOptions(merge: true),
      );
    } else {
      // If it doesn't exist, create a new document
      await quizDocReference.set(favQuiz.toJson());
    }
  }

  Future<void> deleteFromFavShuffleQuizes({
    required String favQuizId,
  }) async {
    final favQuizDocReference = firestore
        .collection('users')
        .doc(userr.value.uid)
        .collection('favShuffleQuizzes')
        .doc(favQuizId);

    // Check if the document already exists
    final favQuizDocSnapshot = await favQuizDocReference.get();

    if (favQuizDocSnapshot.exists) {
      // If it exists, delete the document
      await favQuizDocReference.delete();
    }
  }

  Future<void> nameUpdate() async {
    try {
      await firestore.collection('users').doc(userr.value.uid).update({
        'name': name.value,
      });
    } catch (e) {
      getErrorSnackBar('Nešto je pošlo po zlu, Molimo pokušajte ponovo');
    }

    update();
  }
}
