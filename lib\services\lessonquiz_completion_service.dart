import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LessonQuizCompletionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Check if a lesson/quiz was completed within the last 30 days
  bool isCompletedWithinThirtyDays(
      String id, Map<String, dynamic>? completedMap) {
    if (completedMap == null || !completedMap.containsKey(id)) {
      return false;
    }

    final timestamp = completedMap[id];
    if (timestamp is Timestamp) {
      final completionDate = timestamp.toDate();
      final daysDifference = DateTime.now().difference(completionDate).inDays;
      return daysDifference <= 30;
    }

    return false;
  }

  // Check if a lesson/quiz was opened within the last 30 days
  bool isOpenedWithinThirtyDays(String id, Map<String, dynamic>? openedMap) {
    if (openedMap == null || !openedMap.containsKey(id)) {
      return false;
    }

    final timestamp = openedMap[id];
    if (timestamp is Timestamp) {
      final openedDate = timestamp.toDate();
      final daysDifference = DateTime.now().difference(openedDate).inDays;
      return daysDifference <= 30;
    }

    return false;
  }

  Future<void> updateArticleCount(String articleId) async {
    try {
      final ProfileController profileController = Get.find();
      final completedArticleIds =
          profileController.userr.value.completedArticleIds ?? [];

      // Only add if not already completed
      if (!completedArticleIds.contains(articleId)) {
        final updatedArticleIds = [...completedArticleIds, articleId];

        // Update both Firestore and local state
        await _firestore
            .collection('users')
            .doc(profileController.userr.value.uid!)
            .update({
          'completedArticleIds': FieldValue.arrayUnion([articleId])
        });

        // Update local profile controller
        profileController.userr.value.completedArticleIds = updatedArticleIds;

        print(
            'article updateArticleCount: Added article $articleId, total count: ${updatedArticleIds.length}');

        // Schedule the UI refresh for after the current build cycle
        WidgetsBinding.instance.addPostFrameCallback((_) {
          profileController.userr.refresh();
        });
      }
    } catch (e) {
      print('article updateArticleCount: Error updating article count: $e');
    }
  }

  // Add a lesson or quiz as completed and update Firestore
  Future<void> addCompletedLessonQuizAndUpdateFirestore(
      String userId, String id) async {
    final ProfileController profileController = Get.find();
    try {
      final Timestamp currentTimestamp = Timestamp.now();

      // Fetch current user data
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        // Fetch or initialize the map
        Map<String, dynamic> completedLessonsQuizes =
            userDoc.data()?['completedLessonQuizesInThirtyDays'] ?? {};

        // Clean expired entries before adding new one
        completedLessonsQuizes.removeWhere((lessonId, timestamp) {
          if (timestamp is Timestamp) {
            DateTime completionDate = timestamp.toDate();
            return DateTime.now().difference(completionDate).inDays > 30;
          }
          return true; // Remove if timestamp format is invalid
        });

        // Add or update the lesson with the current timestamp
        completedLessonsQuizes[id] = currentTimestamp;

        // Update Firestore
        await _firestore.collection('users').doc(userId).update({
          'completedLessonQuizesInThirtyDays': completedLessonsQuizes,
        });
        // Update local variable in ProfileController
        profileController.userr.value.completedLessonQuizesInThirtyDays =
            completedLessonsQuizes;
      }
    } catch (e) {
      //
    }
  }

  // Add a lesson or quiz as opened and update Firestore
  Future<void> addOpenedQuizArticleAndUpdateFirestore(
      String userId, String id) async {
    final ProfileController profileController = Get.find();
    try {
      final Timestamp currentTimestamp = Timestamp.now();

      // Fetch current user data
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        // Fetch or initialize the map
        Map<String, dynamic> openedQuizesAndArticles =
            userDoc.data()?['openedQuizesAndArticlesinMonth'] ?? {};

        // Clean expired entries before adding new one
        openedQuizesAndArticles.removeWhere((itemId, timestamp) {
          if (timestamp is Timestamp) {
            DateTime openedDate = timestamp.toDate();
            return DateTime.now().difference(openedDate).inDays > 30;
          }
          return true; // Remove if timestamp format is invalid
        });

        // Add or update the item with the current timestamp
        openedQuizesAndArticles[id] = currentTimestamp;

        // Update Firestore
        await _firestore.collection('users').doc(userId).update({
          'openedQuizesAndArticlesinMonth': openedQuizesAndArticles,
        });
        // Update local variable in ProfileController
        profileController.userr.value.openedQuizesAndArticlesinMonth =
            openedQuizesAndArticles;
      }
    } catch (e) {
      //
    }
  }

  /// Professional cleanup method - handles both 30-day maps efficiently
  Future<void> cleanExpiredDataOnAppLaunch(String userId) async {
    final ProfileController profileController = Get.find();
    try {
      // Fetch current user data once
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      final updates = <String, dynamic>{};
      bool hasUpdates = false;

      // Clean completedLessonQuizesInThirtyDays
      final completedLessons = userData['completedLessonQuizesInThirtyDays']
              as Map<String, dynamic>? ??
          {};
      final originalCompletedSize = completedLessons.length;

      completedLessons.removeWhere((lessonId, timestamp) {
        if (timestamp is Timestamp) {
          final completionDate = timestamp.toDate();
          return DateTime.now().difference(completionDate).inDays > 30;
        }
        return true; // Remove invalid timestamps
      });

      if (completedLessons.length < originalCompletedSize) {
        updates['completedLessonQuizesInThirtyDays'] = completedLessons;
        profileController.userr.value.completedLessonQuizesInThirtyDays =
            completedLessons;
        hasUpdates = true;
      }

      // Clean openedQuizesAndArticlesinMonth
      final openedQuizesAndArticles =
          userData['openedQuizesAndArticlesinMonth'] as Map<String, dynamic>? ??
              {};
      final originalOpenedSize = openedQuizesAndArticles.length;

      openedQuizesAndArticles.removeWhere((itemId, timestamp) {
        if (timestamp is Timestamp) {
          final openedDate = timestamp.toDate();
          return DateTime.now().difference(openedDate).inDays > 30;
        }
        return true; // Remove invalid timestamps
      });

      if (openedQuizesAndArticles.length < originalOpenedSize) {
        updates['openedQuizesAndArticlesinMonth'] = openedQuizesAndArticles;
        profileController.userr.value.openedQuizesAndArticlesinMonth =
            openedQuizesAndArticles;
        hasUpdates = true;
      }

      // Single Firestore update for both maps if needed
      if (hasUpdates) {
        await _firestore.collection('users').doc(userId).update(updates);
        debugPrint('Cleaned expired data: ${updates.keys.join(", ")}');
      }
    } catch (e) {
      debugPrint('Error cleaning expired data: $e');
    }
  }

  /// Legacy method - kept for backward compatibility
  @Deprecated('Use cleanExpiredDataOnAppLaunch instead')
  Future<void> cleanExpiredLessonsQuizes(String userId) async {
    await cleanExpiredDataOnAppLaunch(userId);
  }
}
