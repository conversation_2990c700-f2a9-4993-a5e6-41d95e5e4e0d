import 'package:bibl/controllers/heart_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/reward_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'controllers/lesson_controller.dart';
import 'controllers/quiz_controller.dart';
import 'widgets/customappbar.dart';
import 'widgets/merged_items_list_widget.dart';

class Home extends StatefulWidget {
  final ScrollController scrollController;
  const Home({super.key, required this.scrollController});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  final ProfileController profileController = Get.find();
  final HeartController heartController = Get.find();
  final QuizController quizController = Get.find();
  final LessonController lessonController = Get.find();

  String weekDaysSelected = 'Pon';
  int streak = 0;

  @override
  void initState() {
    super.initState();
    // Delay streak checking to ensure profile data is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (profileController.userr.value.uid != null) {
        checkWeeklyStreak();
        checkConsecutiveStreak();
      }
    });
  }

  void checkWeeklyStreak() async {
    // No delay needed - ProfileController should be ready
    DateTime now = DateTime.now();
// Check if it's a new day based on 22:00 to 6:00 logic
    DateTime startOfNewDay = DateTime(now.year, now.month, now.day, 6);
    DateTime endOfNewDay = DateTime(now.year, now.month, now.day, 22);
    if (now.isAfter(startOfNewDay) || now.isBefore(endOfNewDay)) {
      // Fetch last streak update date (default to 1 day before today)
      DateTime lastUpdateDate =
          profileController.userr.value.lastStreakUpdate?.toDate() ??
              now.subtract(const Duration(days: 1));

      // Check if the streak was already updated today
      if (now.difference(lastUpdateDate).inDays == 0) {
        return;
      }
      // Initialize reward service
      RewardService rewardService =
          RewardService(profileController.userr.value.uid ?? '');

      // Update streak logic
      int currentStreak = profileController.userr.value.weeklyStreak ?? 0;
      int currentHearts = profileController.userr.value.hearts ?? 0;

      if (now.difference(lastUpdateDate).inDays == 1) {
        // User logged in on the next consecutive day: Increment streak
        currentStreak += 1;

        // If streak exceeds 7, reset it
        if (currentStreak > 7) {
          currentStreak = 1;
        }

        // Handle Neuron Rewards for streak milestones
        if (currentStreak == 1) {
          currentHearts += heartController.dailyHearts.value;
        } else if (currentStreak == 2) {
          rewardService.addNeurons(
            50,
          ); // 50 neurons for 2-day streak
          currentHearts += heartController.dailyHearts.value;
        } else if (currentStreak == 3) {
          rewardService.dailyLearner(); // 100 neurons for 3-day streak
          currentHearts += 2;
        } else if (currentStreak == 4) {
          rewardService.addNeurons(150); // 150 neurons for 4-day streak
          currentHearts += 3;
        } else if (currentStreak == 5) {
          rewardService.addNeurons(200); // 200 neurons for 5-day streak
          currentHearts += 4;
        } else if (currentStreak == 6) {
          rewardService.addNeurons(250); // 250 neurons for 6-day streak
          currentHearts += 5;
        } else if (currentStreak == 7) {
          rewardService.weeklyStreak(); // 300 neurons for 7-day streak
          currentHearts += 6;
        }
      } else {
        // User missed a day: Reset streak to 1
        currentStreak = 1;
        currentHearts += heartController.dailyHearts.value;
      }

      // Update local values
      profileController.userr.value.weeklyStreak = currentStreak;
      profileController.userr.value.hearts = currentHearts;
      profileController.userr.value.lastStreakUpdate = Timestamp.now();

      // Sync updated streak and timestamp to Firestore
      await profileController.updateHearts(currentHearts);
      await profileController.updateStreak(currentStreak);
    }
  }

  void checkConsecutiveStreak() async {
    // No delay needed - execute immediately
    DateTime now = DateTime.now();
// Check if it's a new day based on 22:00 to 6:00 logic
    DateTime startOfNewDay = DateTime(now.year, now.month, now.day, 6);
    DateTime endOfNewDay = DateTime(now.year, now.month, now.day, 22);
    if (now.isAfter(startOfNewDay) || now.isBefore(endOfNewDay)) {
      int consecutiveStreak =
          profileController.userr.value.consecutiveStreak ?? 0;
      RewardService rewardService =
          RewardService(profileController.userr.value.uid ?? '');
      DateTime lastActiveDate =
          profileController.userr.value.lastActiveTime?.toDate() ??
              DateTime.now().subtract(const Duration(days: 1));

      // Check if the streak has already been updated today
      if (now.difference(lastActiveDate).inDays == 0) {
        // Streak has already been updated today, no further action required
        return;
      }
      // Check if the user logged in on the next day
      if (now.difference(lastActiveDate).inDays == 1) {
        // User logged in on the next day (consecutive streak continues)
        consecutiveStreak += 1;
        profileController.userr.value.consecutiveStreak = consecutiveStreak;
        profileController.userr.value.lastActiveTime = Timestamp.now();
        profileController.updateConsecutiveStreak(consecutiveStreak);

        // Consecutive Streak Milestone Rewards
        if (consecutiveStreak == 30) {
          rewardService.streakChampion();
        } else if (consecutiveStreak == 60) {
          rewardService.learningAddict();
        } else if (consecutiveStreak == 90) {
          rewardService.loyalUser();
        }

        // Update loyal user streak days for 90-day tracking
        await FirebaseFirestore.instance
            .collection('users')
            .doc(profileController.userr.value.uid)
            .update({
          'loyalUserStreakDays': consecutiveStreak,
        });
        profileController.userr.value.loyalUserStreakDays = consecutiveStreak;
      } else {
        // If not logged in on consecutive days, reset consecutive streak to 1
        consecutiveStreak = 1;
        profileController.userr.value.consecutiveStreak = consecutiveStreak;
        profileController.userr.value.lastActiveTime = Timestamp.now();
        profileController.updateConsecutiveStreak(consecutiveStreak);
      }

      setState(() {}); // Update UI to reflect the current consecutive streak
    }
  }

  @override
  Widget build(BuildContext context) {
    return homeWidget(context);
  }

  homeWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        height: 175.0, // Set height to 175 for Home screen
        widget: SizedBox(
          height: 175.0, // Set a fixed height for the container
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 20, 16, 5),
            child: Column(
              children: [
                const Spacer(
                  flex: 4,
                ),
                Row(
                  children: [
                    const Txt(
                      txt: 'umniLab',
                      fontSize: 20,
                      fontColor: Colors.white,
                    ),
                    const Spacer(),
                    Obx(
                      () => profileController.userr.value.weeklyStreak !=
                                  null &&
                              profileController.userr.value.weeklyStreak! >= 2
                          ? SvgPicture.asset('assets/svgs/fire_icon.svg')
                          : const SizedBox.shrink(),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    SvgPicture.asset('assets/svgs/heart_icon.svg'),
                    const SizedBox(
                      width: 10,
                    ),
                    Obx(
                      () => profileController.userr.value.isPremiumUser == true
                          ? SvgPicture.asset('assets/svgs/infinity_icon.svg')
                          : Txt(
                              txt: (profileController.userr.value.hearts ?? 0)
                                  .toString(),
                              fontSize: 16,
                              fontColor: Colors.white,
                            ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    SvgPicture.asset('assets/svgs/brain_icon.svg'),
                    const SizedBox(
                      width: 5,
                    ),
                    Obx(
                      () => Txt(
                        txt: (profileController.userr.value.neurons ?? 0)
                            .toString(),
                        fontSize: 16,
                        fontColor: Colors.white,
                      ),
                    ),
                  ],
                ),
                const Spacer(
                  flex: 3,
                ),
                daysWidget(),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
      body: MergedItemsList(
        isForLibrary: false,
        scrollController: widget.scrollController,
      ),
    );
  }

  SizedBox daysWidget() {
    List<String> weekDays = [
      'Pon', // Monday
      'Uto', // Tuesday
      'Sre', // Wednesday
      'Čet', // Thursday
      'Pet', // Friday
      'Sub', // Saturday
      'Ned' // Sunday
    ];

    int todayIndex = DateTime.now().weekday - 1; // Monday = 0, Sunday = 6
    int streakLength = profileController.userr.value.weeklyStreak ?? 0;

    // Calculate the first day of the streak
    int streakStartIndex = (todayIndex + 1 - streakLength) % 7;
    if (streakStartIndex < 0) streakStartIndex += 7; // Handle negative index

    // Shift the weekDays list so the streak always looks continuous
    List<String> shiftedDays = [
      ...weekDays.sublist(streakStartIndex), // Start from the first streak day
      ...weekDays.sublist(0, streakStartIndex) // Wrap around to the beginning
    ];

    return SizedBox(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(
          7,
          (index) {
            String title = shiftedDays[index];

            // The first `streakLength` days should be highlighted
            bool isStreakDay = index < streakLength;
            bool isToday = shiftedDays[index] == weekDays[todayIndex];

            return weekDayTitleWidget(title, isStreakDay, isToday);
          },
        ),
      ),
    );
  }

  Column weekDayTitleWidget(String title, bool isStreakDay, bool isToday) {
    return Column(
      children: [
        Container(
          height: 18,
          width: 18,
          padding: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isStreakDay || isToday
                  ? Colors.white
                  : greyishColor.withValues(alpha: 0.5),
              width: 1,
            ),
            color: isStreakDay || isToday ? Colors.white : Colors.transparent,
            boxShadow: isStreakDay || isToday
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : [],
          ),
        ),
        const Spacer(),
        Txt(
          txt: isToday ? 'Danas' : title,
          fontSize: 12,
          fontColor: isStreakDay || isToday ? Colors.white : Colors.grey,
        ),
        isToday
            ? Container(
                height: 2,
                width: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              )
            : Container(
                height: 2,
              ),
      ],
    );
  }
}
