Creating an educational app with quiz questions and a sense of competition packed in a gamified design can make learning more engaging and fun. Here’s a detailed solution for your app. Something similar on market that exist is nibble app

Currencies, points and experience:
Start users with 0 neurons  , neurons represents points and it is a soft currency in the  app, goal is to motivate users to compete and to come back every day by following value model:
- 2 days streak: 50 neurons
- 3 days streak: 100 neurons
- 4 days streak: 150 neurons
- 5 days streak: 200 neurons
- 6 days streak: 250 neurons
- 7 days streak: 300 neurons

This resets after 7 days and starts over again every 7 days. So on Day 8, the user gets 50, on Day 9, the user gets 100, and so on until 300 neurons, and then reset again. So this is simple in the beginning. This reward system is for all users, free and premium.

The app has 40 achievements, which are listed below. Those achievements also reward the user with a certain number of neurons.  Here is the list of the points:

1.	First Steps: Complete your first quiz. – 50 neurons
2.	Quiz Wiz: Complete 10 quizzes. – 150 neurons
3.	Topic Explorer: Open your first topic. – 50 neurons
4.	Daily Learner: Log in for 3 consecutive days. – 100 neurons (only once in a month)
5.	Weekly Streak: Log in for 7 consecutive days. – 200 neurons (every week)
6.	<PERSON>ce Reader: Read 5 articles. – 100 neurons
7.	Intermediate Reader: Read 10 articles. – 150 neurons
8.	Advanced Reader: Read 20 articles. – 200 neurons
9.	First Correct Answer: Get your first quiz question correct. – 10 neurons
10.	Accuracy Ace: Achieve 90% accuracy in a quiz. – 50 neurons (every time when user achieves this)
11.	Point Collector: Earn 1000 points. – 150 neurons
12.	Point Hoarder: Earn 5000 points. – 500 neurons
14.	Silver League Achiever: Reach Silver League. – 500 neurons
15.	Gold League Achiever: Reach Gold League. – 1000 neurons
16.	Quiz Marathoner: Complete 50 quizzes. – 400 neurons
17.	Topic Enthusiast: Open 10 topics. – 200 neurons
18.	Content Connoisseur: Open 20 topics. – 300 neurons
19.	Streak Champion: Maintain a 30-day streak. – 500 neurons
20.	Classic Quiz Master: Complete 20 Classic quizzes. – 250 neurons
21.	Article Enthusiast: Read 50 articles. – 400 neurons
22.	Quiz Veteran: Complete 100 quizzes. – 600 neurons
23.	Learning Addict: Log in every day for 60 days. – 800 neurons
24.	Quiz Whiz: Get 100% on a quiz. – 100 neurons
25.	History Buff: Complete all articles/quizzes in the History category. – 300 neurons (once lifetime)
26.	Mythology Master: Complete all quizzes in the Mythology category. – 300 neurons (once lifetime)
27.	Cosmos Conqueror: Complete all quizzes in the Cosmos category. – 300 neurons (once lifetime)
28.	Science Sage: Complete all quizzes in the Science category. – 300 neurons (once lifetime)
29.	Art Aficionado: Complete all quizzes in the Art category. – 300 neurons (once lifetime)
30.	Marathoner: Read articles for 30 minutes in one session. – 250 neurons (once in 7 days)
31.	Knowledge Seeker: Answer 100 quiz questions correctly. – 800 neurons (once lifetime)
32.	Quiz King/Queen: Rank first in your league. – 300 neurons for every league
33.	Participation Trophy: Participate in 10 league events. – 1000 neurons (once lifetime)
34.	Competitive Spirit: Win 5 league promotions. – 500 neurons (once lifetime)
35.	Consistent Learner: Log in and complete a quiz daily for 14 days. – 200 neurons
36.	Multi-Category Master: Complete quizzes in all categories (articles only). – 800 neurons (once every 30 days)
37.	Power User: Spend over 1000 minutes on the app. – 1000 neurons (every 1000 minutes)
38.	Perfectionist: Get all questions right in 5 consecutive quizzes. – 250 neurons
39.	Loyal User: Maintain a 90-day streak. – 1000 neurons (every 90 days)
40.	Ultimate Learner: Earn every badge in the app. – 3000 neurons


Hard currency is heart ,heart represents how many tries users have to read articles or open new quizzes. By default, when a user signs up for the app gets 5 hearts. A quiz or article that is already been opened once doesn’t require heart currency again for 30 days. A quiz or Article makes neurons (points) only once in 30 days, but can be opened unlimited times once it’s opened once in that period. 

Premium user has an unlimited number of hearts.

How can users earn hearts?

- On the beginning, free users* will earn 5 hearts. When the user spends those 5 hearts, the user is eligible to watch rewarded videos for a quiz or an article. One rewarded video = 1 heart, which means if the user doesn’t have any hearts need to watch a rewarded video for an article or quiz. 
- Free users* will get 3 hearts daily by opening the app for 24 hours. If users visit the app every day, it is eligible for a day streak

- 2 days streak: +1 heart
- 3 days streak: +2 hearts
- 4 days streak: +3 hearts
- 5 days streak: +4 hearts
- 6 days streak: +5 hearts
- 7 days streak: +6 hearts

This resets again after 7 days, on day 8 the user gets only 0 hearts, and on day 9 the user gets 1 heart, and so on, same approach as neurons, but this is only for free users.
Premium users have unlimited hearts 



Neurons (Points) System for the Quiz questions

 Points for Correct Answers:
   - True/False Questions: 5 points for each correct answer; maximum per shuffle quiz 50 points, 10 questions.
   - Multiple Choice Questions: 10 points for each correct answer. Maximum per usual quiz: 100 points (neurons), 10 questions.
- Article questions: 10 points = Maximum 50

* All quizzes and articles can be used only once per month, so allow users to answer every quiz or article only once per month.

* All points accumulated represent the experience as well, which is not represented to users for now in the background, we count it, and based onthe total points achieved, users will be promoted in different tiers. Titles will be presented below.


Quiz Models

1. Shuffle/ Swipe Quiz (True or False):
   - Users swipe right for true and left for false.
   - Points are awarded for correct answers.

2. Question/Answer Classic Quiz:
   - Standard multiple-choice format.
   - Points are awarded for correct answers.

 Articles and Questions

- Each topic contains 5 articles.
- Each article ends with one question related to its content.
- Correctly answering a question at the end of an article earns the user points (as specified above).

Leaderboards and Leagues

- 20 Leagues System:

  - Users are grouped into leagues based on their points.
  - The top league is the highest level, and the lowest is for beginners.
  - Regular promotion and demotion based on performance to keep competition active.
  - Users that are going in the league are up our top 3, and the last three are regulated (going down or staying in the same if it’s a beginner league at the begininig)
  - Leagues last for one week.

- Weekly Competition: Leagues run for one week, starting on Monday and ending on Sunday.
- Placement: You're placed in a league with 20 other users based on their XP (experience points) from the previous week. 
- If the user doesn’t show up in the app for 30 days, neurons go back to 0

  
League Names and Durations: 

1. Bronze League (1 week) - Bronzana Liga (1 nedelja)
2. Silver League (1 week) - Srebrna Liga (1 nedelja)
3. Gold League (1 week) - Zlatna Liga (1 nedelja)
4. Platinum League (1 week) - Platinum Liga (1 nedelja)
5. Diamond League (1 week) - Dijamantska Liga (1 nedelja)
6. Ruby League (1 week) - Rubin Liga (1 nedelja)
7. Sapphire League (1 week) - Safirna Liga (1 nedelja)
8. Emerald League (1 week) - Smaragdna Liga (1 nedelja)
9. Topaz League (1 week) - Topaz Liga (1 nedelja)
10. Amethyst League (1 week) - Ametist Liga (1 nedelja)
11. Opal League (1 week) - Opal Liga (1 nedelja)
12. Quartz League (1 week) - Kvarc Liga (1 nedelja)
13. Jade League (1 week) - Žad Liga (1 nedelja)
14. Crystal League (1 week) - Kristalna Liga (1 nedelja)
15. Fenix League (1 week) - Fenix Liga (1 nedelja)
16. Onyx League (1 week) - Oniks Liga (1 nedelja)
17. Galaxy League (1 week) - Galakisjksa Liga (1 nedelja)
18. Coral League (1 week) - Koralska Liga (1 nedelja)
19. Jupiter League (1 week) - Jupiter Liga (1 nedelja)
20. Elite League (1 week) - Elitna Liga (1 nedelja)

___________________________________________________________________________
Case for Hamza (Developer)

 1. League Grouping
●	Description: Users are grouped into different leagues based on their XP points earned from the previous week.
●	Scenario:
○	On Monday at 00:01, the system reviews the total XP points accumulated by users during the previous week (Monday 00:00 to Sunday 23:59).
○	Based on their total XP, users are assigned to a league. For example:
■	Beginners start in the Bronzana Liga.
■	Users with higher XP from the previous week are promoted to leagues like Srebrna Liga, Zlatna Liga, etc.
■	The top three in each league are promoted to the next league tier.
■	The last three users are demoted to the lower league, unless they are already in the Bronzana Liga.
2. Promotion and Demotion
●	Description: Regular promotion and demotion to maintain a competitive dynamic.
●	Scenario:
○	At the end of each league week (Sunday 23:59), the system evaluates the user rankings.
○	The top 3 users from each league are promoted to the next league up.
■	For example, users finishing in the top 3 in Srebrna Liga are promoted to Zlatna Liga.
○	The bottom 3 users are demoted to the lower league unless they are already in Bronzana Liga.
■	If users are already in Bronzana Liga and are among the bottom 3, they remain in the Bronzana Liga.
○	If the user is inactive (no XP earned), they will automatically be considered among the bottom 3 for demotion.
3. Weekly Competition
●	Description: Leagues last one week, from Monday 00:00 to Sunday 23:59.
●	Scenario:
○	A new league competition starts every Monday at 00:00.
○	During the week, users earn XP by completing app activities (e.g., answering quiz questions, completing tasks).
○	XP is accumulated throughout the week and updated in real-time on the leaderboard for the league.
○	At the end of the week, Sunday 23:59, the league standings are finalized based on the total XP points.
○	Once standings are finalized, promotions and demotions are calculated as described above.
4. XP Calculation
●	Description: XP determines league placement and movement.
●	Scenario:
○	Users gain XP from various activities in the app, such as answering quiz questions or participating in educational tasks.
○	XP is tallied during the week and is reset to zero at the beginning of the new week for the purpose of weekly competition.
○	Historical XP is still stored to track long-term progress, but weekly XP only affects current league status.
5. League Names and Tiers
●	Description: Leagues have distinct Serbian names and represent different tiers of competition.
●	Scenario:
○	Upon league assignment, users will see the name of the league they are in for the week. For example:
■	Beginners will see "Dobrodošli u Bronzanu Ligu!" when they enter the Bronzana Liga.
■	Users promoted to higher leagues will receive a congratulatory message like "Čestitamo, prešli ste u Srebrnu Ligu!".
○	The league name will be displayed prominently on their dashboard along with their current rank.
○	The leagues, ordered from lowest to highest, are:
■	Bronzana Liga
■	Srebrna Liga
■	Zlatna Liga
■	Platinum Liga
■	Dijamantska Liga
■	Rubin Liga
■	Safirna Liga
■	Smaragdna Liga
■	Topaz Liga
■	Ametist Liga
■	Opal Liga
■	Kvarc Liga
■	Žad Liga
■	Kristalna Liga
■	Fenix Liga
■	Oniks Liga
■	Galaktička Liga
■	Koralska Liga
■	Jupiter Liga
■	Elitna Liga
6. League Transitions
●	Description: Users are notified of their league transitions (promotion, demotion, or staying).
●	Scenario:
○	On Monday at 00:00, when the new week begins:
■	Users who were promoted will receive a notification: "Čestitamo! Promovisani ste u [next league name]."
■	Users who were demoted will receive a notification: "Nažalost, vraćeni ste u [previous league name]."
■	Users who stayed in the same league will receive a notification: "Ostali ste u [current league name] za ovu nedelju."
7. Edge Cases
●	Inactive Users: Users who do not earn any XP during the week are automatically considered for demotion if not in the beginner league.
●	Tie Breakers: In the case of a tie in XP (e.g., two users with the same score):
○	The user who reached the score earlier in the week takes precedence for promotion.
○	If tied users are in the demotion zone, the same rule applies but in reverse; the last user to reach the score is demoted.




END OF CASE
________________________________________________________________________




 
User Titles and Achievements

User Titles [Design is ready]


* All points accumulated represents the experience as well which is not represented to users for now in background we count it and based on total points achieved users will be promoted in different tiers.




1. Newcomer - Početnik - until 3,000 exp
2. Apprentice - Pripravnik - when user reach 6,000 exp
3. Adept - Vešt - when user reach 10,000 exp
4. Proficient - Stručnjak - when user reach 15,000 exp
5. Specialist - Specijalista - when user reach 20,000 exp
6. Expert - Ekspert - when user reach 30,000 exp
7. Veteran - Veteran - when user reach 40,000 exp
8. Master - Majstor - when user reach 50,000 exp
9. Virtuoso - Virtuoz - when user reach 60,000 exp
10. Sage - Mudrac - when user reach 70,000 exp
11. Grandmaster - Velemajstor - when user reach 80,000 exp
12. Luminary - Superstar - when user reach 90,000 exp


We will use Serbian language and copies will be provided in figma.


 Achievements and Badges [Design Needed]:

  50 badges for their achievements with varying difficulty:

    1. First Steps: Complete your first quiz.
    2. Quiz Wiz: Complete 10 quizzes.
    3. Topic Explorer: Open your first topic.
    4. Daily Learner: Log in for 3 consecutive days.
    5. Weekly Streak: Log in for 7 consecutive days.
    6. Novice Reader: Read 5 articles.
    7. Intermediate Reader: Read 10 articles.
    8. Advanced Reader: Read 20 articles.
    9. First Correct Answer: Get your first quiz question correct.
    10. Accuracy Ace: Achieve 90% accuracy in a quiz.
    11. Point Collector: Earn 1000 points.
    12. Point Hoarder: Earn 5000 points.
    14. Silver League Achiever: Reach Silver League.
    15. Gold League Achiever: Reach Gold League.
    16. Quiz Marathoner: Complete 50 quizzes.
    17. Topic Enthusiast: Open 10 topics.
    18. Content Connoisseur: Open 20 topics.
    19. Streak Champion: Maintain a 30-day streak.
    20. True/False Master: Complete 20 True/False quizzes.
    21. Classic Quiz Master: Complete 20 Classic quizzes.
    22. Article Enthusiast: Read 50 articles.
    23. Quiz Veteran: Complete 100 quizzes.
    24. Learning Addict: Log in every day for 60 days.
    25. Speed Reader: Read an article in under 2 minutes.
    26. Quiz Whiz: Get 100% on a quiz.
    27. History Buff: Complete all quizzes in the History category.
    28. Mythology Master: Complete all quizzes in the Mythology category.
    29. Cosmos Conqueror: Complete all quizzes in the Cosmos category.
    30. Science Sage: Complete all quizzes in the Science category.
    31. Art Aficionado: Complete all quizzes in the Art category.
    32. Marathoner: Read articles for 30 minutes in one session.
    33. Knowledge Seeker: Answer 100 quiz questions correctly.
    34. Quiz King/Queen: Rank first in your league.
    35. Participation Trophy: Participate in 10 league events.
    36. Competitive Spirit: Win 5 league promotions.
    37. Quick Thinker: Answer a quiz question correctly within 5 seconds.
    38. Consistent Learner: Log in and complete a quiz daily for 14 days.
    39. Multi-Category Master: Complete quizzes in all categories.
    40. Ad Watcher: Watch 10 rewarded ads.
    41. Ad Free: Use points to skip ads 10 times.
    42. Early Adopter: Be one of the first 1000 users to join.
    43. Power User: Spend over 1000 minutes on the app.
    44. Community Helper: Refer 5 friends to the app.
    45. Feedback Provider: Submit feedback or a review.
    46. Bug Finder: Report a bug.
    47. Perfectionist: Get all questions right in 5 consecutive quizzes.
    48. Quiz Creator: Submit a quiz question suggestion.
    49. Loyal User: Maintain a 90-day streak.
    50. Ultimate Learner: Earn every badge in the app.










Serbian Naming (for Developer)

1. Prvi koraci! Završili ste svoj prvi kviz.
2. Majstor kvizova: Završite 10 kvizova.
3. Istraživač tema: Otvorite svoju prvu temu.
4. Dnevni učenik: Prijavite se 3 uzastopna dana.
5. Nedeljni niz! Prijavite se 7 uzastopnih dana.
6. Početnik čitalac: Pročitajte 5 članaka.
7. Srednji čitalac: Pročitajte 10 članaka.
8. Napredni čitalac: Pročitajte 20 članaka.
9. Pravilno odgovoreno: Dobijte svoj prvi tačan odgovor na kvizu.
10. As preciznosti: Postignite 90% tačnosti na kvizu.
11. Sakupljač poena: Zaradite 1000 poena.
12. Čuvar poena: Zaradite 5000 poena.
14. Postignuće srebrne lige: Dostignite Srebrni rang.
15. Postignuće zlatne lige: Dostignite Zlatni rang.
16. Maratonac kvizova: Završite 50 kvizova.
17. Entuzijasta za teme: Otvorite 10 tema.
18. Poznavalac sadržaja: Otvorite 20 tema.
19. Prvak niza: Održite niz od 30 dana.
20. Majstor istinito/nesigurno: Završite 20 istinito/nesigurnih kvizova.
21. Majstor klasičnih kvizova: Završite 20 klasičnih kvizova.
22. Entuzijasta za članke: Pročitajte 50 članaka.
23. Veteran kvizova: Završite 100 kvizova.
24. Ovisnik o učenju: Prijavite se svakog dana 60 dana.
25. Brzi čitalac: Pročitajte članak za manje od 2 minuta.
26. Kviz guru: Osvojite 100% na kvizu.
27. Ljubitelj storije! Završili te 10 kvizova u kategoriji Istorija.
28. Majstor mitologije! Završili te 10 kvizova u kategoriji Mitologija.
29. Osvojitelj Kosmosa! Završili te 10 kvizova u kategoriji Kosmos.
30. Mudrac nauke! Završili te 10 kvizova u kategoriji Nauka.
31. Aficionado umetnosti: Završite sve kvizove u kategoriji Umetnost.
32. Maratonac: Čitajte članke 30 minuta u jednoj sesiji.
33. Tragač znanja: Odgovorite tačno na 100 pitanja na kvizu.
34. Kralj/Kraljica kvizova: Budite prvi u svojoj ligi.
35. Trofej za učešće: Učestvujte u 10 ligaskih događaja.
36. Takmičarski duh: Osvojite 5 promocija u ligi38. Dosledni učenik: Prijavite se i završite kviz svakodnevno 14 dana.
39. Majstor u više kategorija: Završite kvizove u bar 5 kategorija.
43. Moćni korisnik: Provedite više od 1000 minuta na aplikaciji.
44. Pomoćnik zajednice: Preporučite 5 prijatelja aplikaciji.
47. Perfekcionista: Tačno odgovorite na sva pitanja u 5 uzastopna kviza.
49. Verni korisnik: Održite niz od 90 dana.
50. Ultimativni učenik: Zaradite svaki bedž u aplikaciji.


These achievements need to be coded and need to be presented after the user closes a quiz or article. Designs are ready for this.












4. Rewarded Ads:
   - Allow Free users to watch ads in exchange for hearts, 1 rewarded ad = 1 heart. So once user has 0 hearts, if user wants to open any piece of the content user needs to watch reawreded ads.
   - Offer this as an option when users run low on points.














